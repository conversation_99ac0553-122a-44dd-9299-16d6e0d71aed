package com.datayes.lineage

import com.datayes.dataexchange.DataExchangeJob
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.jdbc.support.GeneratedKeyHolder
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.sql.Statement
import java.time.LocalDateTime
import com.datayes.util.CronExpressionUtil

/**
 * 血缘数据访问层 (Lineage Data Access Layer)
 *
 * 负责血缘信息的持久化和查询操作
 */
@Repository
class LineageRepository(private val jdbcTemplate: JdbcTemplate) {

    /**
     * 更新数据库中的血缘信息
     */
    @Transactional
    fun updateLineageInDatabase(jobKey: String, dataLineage: DataLineage, job: DataExchangeJob, taskId: Long? = null): List<Long> {
        // 1. 标记旧的血缘关系为非活跃
        deactivateLineageByJobKey(jobKey)

        // 2. 保存新的血缘信息（传入taskId和jobKey）
        val relationshipIds = saveDataLineage(dataLineage, taskId, jobKey)

        // 3. 更新引用计数
        updateReferenceCounts(dataLineage)

        return relationshipIds
    }

    /**
     * 更新表和列的引用计数
     */
    fun updateReferenceCounts(dataLineage: DataLineage) {
        // 增加新引用的计数
        dataLineage.tableLineage.sourceTables.forEach { table ->
            incrementTableReferenceCount(table)
        }
        incrementTableReferenceCount(dataLineage.tableLineage.targetTable)

        dataLineage.columnLineages.forEach { columnLineage ->
            incrementColumnReferenceCount(columnLineage.sourceColumn)
            incrementColumnReferenceCount(columnLineage.targetColumn)
        }
    }

    /**
     * 增加表引用计数
     */
    fun incrementTableReferenceCount(tableInfo: TableInfo) {
        val sql = """
            UPDATE lineage_tables 
            SET reference_count = reference_count + 1, 
                last_referenced_at = CURRENT_TIMESTAMP
            WHERE datasource_id = (
                SELECT id FROM lineage_datasources 
                WHERE host = ? AND port = ? AND database_name = ?
            ) AND table_name = ? AND (schema_name = ? OR (schema_name IS NULL AND ? IS NULL))
        """.trimIndent()

        jdbcTemplate.update(
            sql,
            tableInfo.database.host,
            tableInfo.database.port,
            tableInfo.database.databaseName,
            tableInfo.tableName,
            tableInfo.schema,
            tableInfo.schema
        )
    }

    /**
     * 增加列引用计数
     */
    fun incrementColumnReferenceCount(columnInfo: ColumnInfo) {
        val sql = """
            UPDATE lineage_columns 
            SET reference_count = reference_count + 1,
                last_referenced_at = CURRENT_TIMESTAMP
            WHERE table_id IN (
                SELECT t.id FROM lineage_tables t
                JOIN lineage_datasources ds ON t.datasource_id = ds.id
                WHERE ds.host = ? AND ds.port = ? AND ds.database_name = ?
                  AND t.table_name = ? AND (t.schema_name = ? OR (t.schema_name IS NULL AND ? IS NULL))
            ) AND column_name = ?
        """.trimIndent()

        jdbcTemplate.update(
            sql,
            columnInfo.table.database.host,
            columnInfo.table.database.port,
            columnInfo.table.database.databaseName,
            columnInfo.table.tableName,
            columnInfo.table.schema,
            columnInfo.table.schema,
            columnInfo.columnName
        )
    }

    /**
     * 根据作业标识停用血缘关系
     */
    @Transactional
    fun deactivateLineageByJobKey(jobKey: String): Int {
        val sql = """
            UPDATE lineage_relationships 
            SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
            WHERE job_key = ? AND is_active = TRUE
        """.trimIndent()

        return jdbcTemplate.update(sql, jobKey)
    }

    /**
     * 保存数据血缘信息到数据库
     *
     * @param dataLineage 数据血缘对象
     * @param taskId 关联的任务ID
     * @param jobKey 作业唯一标识
     * @return 生成的血缘关系ID列表
     */
    @Transactional
    fun saveDataLineage(dataLineage: DataLineage, taskId: Long?, jobKey: String? = null): List<Long> {
        val relationshipIds = mutableListOf<Long>()

        // 1. 确保数据源存在
        val sourceDataSourceId = getOrCreateDataSource(dataLineage.sourceDatabase)
        val targetDataSourceId = getOrCreateDataSource(dataLineage.targetDatabase)

        // 2. 确保表存在
        val sourceTableIds = dataLineage.tableLineage.sourceTables.map { sourceTable ->
            getOrCreateTable(sourceTable, sourceDataSourceId)
        }
        val targetTableId = getOrCreateTable(dataLineage.tableLineage.targetTable, targetDataSourceId)

        // 3. 计算内容哈希（如果提供了jobKey）
        val contentHash = if (jobKey != null) LineageHashCalculator.calculateHash(dataLineage) else null

        // 4. 保存表级血缘关系
        for (sourceTableId in sourceTableIds) {
            val tableRelationshipId = saveTableLineageRelationship(
                taskId = taskId,
                sourceTableId = sourceTableId,
                targetTableId = targetTableId,
                lineageType = dataLineage.tableLineage.lineageType,
                originalSql = dataLineage.originalSql,
                jobKey = jobKey,
                contentHash = contentHash
            )
            relationshipIds.add(tableRelationshipId)
        }

        // 5. 保存列级血缘关系
        for (columnLineage in dataLineage.columnLineages) {
            // 确保列存在
            val sourceColumnId = getOrCreateColumn(columnLineage.sourceColumn)
            val targetColumnId = getOrCreateColumn(columnLineage.targetColumn)

            // 获取对应的表ID
            val sourceTableId = getOrCreateTable(
                columnLineage.sourceColumn.table,
                getOrCreateDataSource(columnLineage.sourceColumn.table.database)
            )
            val targetTableId = getOrCreateTable(
                columnLineage.targetColumn.table,
                getOrCreateDataSource(columnLineage.targetColumn.table.database)
            )

            // 保存列级血缘关系
            val columnRelationshipId = saveColumnLineageRelationship(
                taskId = taskId,
                sourceTableId = sourceTableId,
                targetTableId = targetTableId,
                sourceColumnId = sourceColumnId,
                targetColumnId = targetColumnId,
                transformation = columnLineage.transformation,
                jobKey = jobKey,
                contentHash = contentHash
            )
            relationshipIds.add(columnRelationshipId)
        }

        return relationshipIds
    }

    /**
     * 根据表名查询上游血缘关系
     */
    fun findUpstreamLineage(
        tableName: String,
        schemaName: String?,
        datasourceName: String,
        maxLevels: Int = 3
    ): List<TableLineageView> {
        val sql = """
            WITH RECURSIVE upstream_lineage AS (
                SELECT 
                    lr.id as relationship_id,
                    lr.source_table_id,
                    lr.target_table_id,
                    st.table_name as source_table,
                    st.schema_name as source_schema,
                    st.chinese_name as source_chinese_name,
                    sds.datasource_name as source_datasource,
                    ssys.system_name as source_system,
                    tt.table_name as target_table,
                    tt.schema_name as target_schema,
                    tds.datasource_name as target_datasource,
                    lr.lineage_type,
                    lr.source_system as lineage_source,
                    lr.confidence_score,
                    lr.created_at,
                    1 as level
                FROM lineage_relationships lr
                JOIN lineage_tables st ON lr.source_table_id = st.id
                JOIN lineage_tables tt ON lr.target_table_id = tt.id
                JOIN lineage_datasources sds ON st.datasource_id = sds.id
                JOIN lineage_datasources tds ON tt.datasource_id = tds.id
                LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
                WHERE tt.table_name = ?
                  AND (? IS NULL OR tt.schema_name = ?)
                  AND tds.datasource_name = ?
                  AND lr.relationship_type = 'TABLE_LEVEL'
                  AND lr.is_active = true
                
                UNION ALL
                
                SELECT 
                    lr.id,
                    lr.source_table_id,
                    lr.target_table_id,
                    st.table_name,
                    st.schema_name,
                    st.chinese_name,
                    sds.datasource_name,
                    ssys.system_name,
                    tt.table_name,
                    tt.schema_name,
                    tds.datasource_name,
                    lr.lineage_type,
                    lr.source_system,
                    lr.confidence_score,
                    lr.created_at,
                    ul.level + 1
                FROM lineage_relationships lr
                JOIN lineage_tables st ON lr.source_table_id = st.id
                JOIN lineage_tables tt ON lr.target_table_id = tt.id
                JOIN lineage_datasources sds ON st.datasource_id = sds.id
                JOIN lineage_datasources tds ON tt.datasource_id = tds.id
                LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
                JOIN upstream_lineage ul ON ul.source_table_id = lr.target_table_id
                WHERE ul.level < ?
                  AND lr.relationship_type = 'TABLE_LEVEL'
                  AND lr.is_active = true
            )
            SELECT * FROM upstream_lineage
            ORDER BY level, source_table
        """.trimIndent()

        return jdbcTemplate.query(
            sql,
            arrayOf(tableName, schemaName, schemaName, datasourceName, maxLevels),
            tableLineageViewRowMapper
        )
    }

    /**
     * 根据表名查询下游血缘关系
     */
    fun findDownstreamLineage(
        tableName: String,
        schemaName: String?,
        datasourceName: String,
        maxLevels: Int = 3
    ): List<TableLineageView> {
        val sql = """
            WITH RECURSIVE downstream_lineage AS (
                SELECT 
                    lr.id as relationship_id,
                    lr.source_table_id,
                    lr.target_table_id,
                    st.table_name as source_table,
                    st.schema_name as source_schema,
                    st.chinese_name as source_chinese_name,
                    sds.datasource_name as source_datasource,
                    ssys.system_name as source_system,
                    tt.table_name as target_table,
                    tt.schema_name as target_schema,
                    tds.datasource_name as target_datasource,
                    lr.lineage_type,
                    lr.source_system as lineage_source,
                    lr.confidence_score,
                    lr.created_at,
                    1 as level
                FROM lineage_relationships lr
                JOIN lineage_tables st ON lr.source_table_id = st.id
                JOIN lineage_tables tt ON lr.target_table_id = tt.id
                JOIN lineage_datasources sds ON st.datasource_id = sds.id
                JOIN lineage_datasources tds ON tt.datasource_id = tds.id
                LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
                WHERE st.table_name = ?
                  AND (? IS NULL OR st.schema_name = ?)
                  AND sds.datasource_name = ?
                  AND lr.relationship_type = 'TABLE_LEVEL'
                  AND lr.is_active = true
                
                UNION ALL
                
                SELECT 
                    lr.id,
                    lr.source_table_id,
                    lr.target_table_id,
                    st.table_name,
                    st.schema_name,
                    st.chinese_name,
                    sds.datasource_name,
                    ssys.system_name,
                    tt.table_name,
                    tt.schema_name,
                    tds.datasource_name,
                    lr.lineage_type,
                    lr.source_system,
                    lr.confidence_score,
                    lr.created_at,
                    dl.level + 1
                FROM lineage_relationships lr
                JOIN lineage_tables st ON lr.source_table_id = st.id
                JOIN lineage_tables tt ON lr.target_table_id = tt.id
                JOIN lineage_datasources sds ON st.datasource_id = sds.id
                JOIN lineage_datasources tds ON tt.datasource_id = tds.id
                LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
                JOIN downstream_lineage dl ON dl.target_table_id = lr.source_table_id
                WHERE dl.level < ?
                  AND lr.relationship_type = 'TABLE_LEVEL'
                  AND lr.is_active = true
            )
            SELECT * FROM downstream_lineage
            ORDER BY level, target_table
        """.trimIndent()

        return jdbcTemplate.query(
            sql,
            arrayOf(tableName, schemaName, schemaName, datasourceName, maxLevels),
            tableLineageViewRowMapper
        )
    }

    /**
     * 查询列级血缘关系
     */
    fun findColumnLineage(
        tableName: String,
        schemaName: String?,
        datasourceName: String
    ): List<ColumnLineageView> {
        val sql = """
            SELECT 
                lr.id as relationship_id,
                sc.column_name as source_column,
                sc.data_type as source_data_type,
                st.table_name as source_table,
                st.schema_name as source_schema,
                tc.column_name as target_column,
                tc.data_type as target_data_type,
                tt.table_name as target_table,
                tt.schema_name as target_schema,
                lr.transformation_type,
                lr.transformation_description,
                lr.transformation_expression,
                lr.confidence_score,
                lr.source_system as lineage_source,
                lr.created_at
            FROM lineage_relationships lr
            JOIN lineage_columns sc ON lr.source_column_id = sc.id
            JOIN lineage_columns tc ON lr.target_column_id = tc.id
            JOIN lineage_tables st ON sc.table_id = st.id
            JOIN lineage_tables tt ON tc.table_id = tt.id
            JOIN lineage_datasources sds ON st.datasource_id = sds.id
            JOIN lineage_datasources tds ON tt.datasource_id = tds.id
            WHERE lr.relationship_type = 'COLUMN_LEVEL'
              AND lr.is_active = true
              AND ((st.table_name = ? AND (? IS NULL OR st.schema_name = ?) AND sds.datasource_name = ?)
                   OR (tt.table_name = ? AND (? IS NULL OR tt.schema_name = ?) AND tds.datasource_name = ?))
            ORDER BY st.table_name, sc.column_name, tt.table_name, tc.column_name
        """.trimIndent()

        return jdbcTemplate.query(
            sql,
            arrayOf(
                tableName, schemaName, schemaName, datasourceName,
                tableName, schemaName, schemaName, datasourceName
            ),
            columnLineageViewRowMapper
        )
    }

    /**
     * 按系统统计血缘信息
     */
    fun getSystemStatistics(): List<SystemStatistics> {
        val sql = """
            SELECT 
                sys.system_name,
                sys.system_code,
                COUNT(DISTINCT ds.id) as datasource_count,
                COUNT(DISTINCT t.id) as table_count,
                COUNT(DISTINCT c.id) as column_count,
                COUNT(DISTINCT CASE WHEN lr.relationship_type = 'TABLE_LEVEL' THEN lr.id END) as table_lineage_count,
                COUNT(DISTINCT CASE WHEN lr.relationship_type = 'COLUMN_LEVEL' THEN lr.id END) as column_lineage_count,
                MAX(lr.created_at) as last_update_time
            FROM lineage_systems sys
            LEFT JOIN lineage_datasources ds ON sys.id = ds.system_id AND ds.status = 'ACTIVE'
            LEFT JOIN lineage_tables t ON ds.id = t.datasource_id AND t.status = 'ACTIVE'
            LEFT JOIN lineage_columns c ON t.id = c.table_id AND c.status = 'ACTIVE'
            LEFT JOIN lineage_relationships lr ON (t.id = lr.source_table_id OR t.id = lr.target_table_id) AND lr.is_active = true
            WHERE sys.status = 'ACTIVE'
            GROUP BY sys.id, sys.system_name, sys.system_code
            ORDER BY sys.system_name
        """.trimIndent()

        return jdbcTemplate.query(sql, systemStatisticsRowMapper)
    }

    // 私有辅助方法

    private fun getOrCreateDataSource(databaseInfo: DatabaseInfo): Long {
        // 首先查询是否已存在
        val existingId = jdbcTemplate.queryForList(
            """
            SELECT id FROM lineage_datasources 
            WHERE db_type = ? AND host = ? AND port = ? AND database_name = ?
            """.trimIndent(),
            Long::class.java,
            databaseInfo.dbType, databaseInfo.host, databaseInfo.port, databaseInfo.databaseName
        ).firstOrNull() ?: run {
            // 不存在则创建
            val keyHolder = GeneratedKeyHolder()
            jdbcTemplate.update({ connection ->
                val ps = connection.prepareStatement(
                    """
                    INSERT INTO lineage_datasources (datasource_name, db_type, host, port, database_name, connection_string)
                    VALUES (?, ?, ?, ?, ?, ?)
                    """.trimIndent(),
                    Statement.RETURN_GENERATED_KEYS
                )
                ps.setString(1, "${databaseInfo.dbType}-${databaseInfo.host}-${databaseInfo.databaseName}")
                ps.setString(2, databaseInfo.dbType)
                ps.setString(3, databaseInfo.host)
                ps.setInt(4, databaseInfo.port)
                ps.setString(5, databaseInfo.databaseName)
                ps.setString(6, databaseInfo.originalConnectionString)
                ps
            }, keyHolder)
            keyHolder.key!!.toLong()
        }
        return existingId
    }

    private fun getOrCreateTable(tableInfo: TableInfo, datasourceId: Long): Long {
        // 首先查询是否已存在
        val existingId = jdbcTemplate.queryForList(
            """
            SELECT id FROM lineage_tables 
            WHERE datasource_id = ? AND table_name = ? AND (schema_name = ? OR (schema_name IS NULL AND ? IS NULL))
            """.trimIndent(),
            Long::class.java,
            datasourceId, tableInfo.tableName, tableInfo.schema, tableInfo.schema
        ).singleOrNull() ?: run {
            // 不存在则创建
            val keyHolder = GeneratedKeyHolder()
            jdbcTemplate.update({ connection ->
                val ps = connection.prepareStatement(
                    """
                    INSERT INTO lineage_tables (datasource_id, schema_name, table_name)
                    VALUES (?, ?, ?)
                    """.trimIndent(),
                    Statement.RETURN_GENERATED_KEYS
                )
                ps.setLong(1, datasourceId)
                ps.setString(2, tableInfo.schema)
                ps.setString(3, tableInfo.tableName)
                ps
            }, keyHolder)
            keyHolder.key!!.toLong()
        }
        return existingId
    }

    private fun getOrCreateColumn(columnInfo: ColumnInfo): Long {
        // 首先获取表ID
        val tableId = getOrCreateTable(
            columnInfo.table,
            getOrCreateDataSource(columnInfo.table.database)
        )

        // 查询是否已存在列
        val existingId = jdbcTemplate.queryForList(
            """
            SELECT id FROM lineage_columns 
            WHERE table_id = ? AND column_name = ?
            """.trimIndent(),
            Long::class.java,
            tableId, columnInfo.columnName
        ).singleOrNull() ?: run {
            // 不存在则创建
            val keyHolder = GeneratedKeyHolder()
            jdbcTemplate.update({ connection ->
                val ps = connection.prepareStatement(
                    """
                    INSERT INTO lineage_columns (table_id, column_name, data_type, column_comment)
                    VALUES (?, ?, ?, ?)
                    """.trimIndent(),
                    Statement.RETURN_GENERATED_KEYS
                )
                ps.setLong(1, tableId)
                ps.setString(2, columnInfo.columnName)
                ps.setString(3, columnInfo.dataType)
                ps.setString(4, columnInfo.comment)
                ps
            }, keyHolder)
            keyHolder.key!!.toLong()
        }
        return existingId
    }

    private fun saveTableLineageRelationship(
        taskId: Long?,
        sourceTableId: Long,
        targetTableId: Long,
        lineageType: LineageType,
        originalSql: String?,
        jobKey: String? = null,
        contentHash: String? = null
    ): Long {
        val keyHolder = GeneratedKeyHolder()
        jdbcTemplate.update({ connection ->
            val ps = connection.prepareStatement(
                """
                INSERT INTO lineage_relationships 
                (task_id, relationship_type, source_table_id, target_table_id, lineage_type, transformation_description, job_key, content_hash)
                VALUES (?, 'TABLE_LEVEL', ?, ?, ?, ?, ?, ?)
                """.trimIndent(),
                Statement.RETURN_GENERATED_KEYS
            )
            ps.setObject(1, taskId)
            ps.setLong(2, sourceTableId)
            ps.setLong(3, targetTableId)
            ps.setString(4, lineageType.name)
            ps.setString(5, originalSql)
            ps.setString(6, jobKey)
            ps.setString(7, contentHash)
            ps
        }, keyHolder)
        return keyHolder.key!!.toLong()
    }

    private fun saveColumnLineageRelationship(
        taskId: Long?,
        sourceTableId: Long,
        targetTableId: Long,
        sourceColumnId: Long,
        targetColumnId: Long,
        transformation: DataTransformation?,
        jobKey: String? = null,
        contentHash: String? = null
    ): Long {
        val keyHolder = GeneratedKeyHolder()
        jdbcTemplate.update({ connection ->
            val ps = connection.prepareStatement(
                """
                INSERT INTO lineage_relationships 
                (task_id, relationship_type, source_table_id, target_table_id, source_column_id, target_column_id, 
                 transformation_type, transformation_description, transformation_expression, job_key, content_hash)
                VALUES (?, 'COLUMN_LEVEL', ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """.trimIndent(),
                Statement.RETURN_GENERATED_KEYS
            )
            ps.setObject(1, taskId)
            ps.setLong(2, sourceTableId)
            ps.setLong(3, targetTableId)
            ps.setLong(4, sourceColumnId)
            ps.setLong(5, targetColumnId)
            ps.setString(6, transformation?.transformationType?.name)
            ps.setString(7, transformation?.description)
            ps.setString(8, transformation?.expression)
            ps.setString(9, jobKey)
            ps.setString(10, contentHash)
            ps
        }, keyHolder)
        return keyHolder.key!!.toLong()
    }

    // RowMapper 定义
    private val tableLineageViewRowMapper = RowMapper<TableLineageView> { rs, _ ->
        TableLineageView(
            relationshipId = rs.getLong("relationship_id"),
            sourceTableId = rs.getLong("source_table_id"),
            targetTableId = rs.getLong("target_table_id"),
            sourceTable = rs.getString("source_table"),
            sourceSchema = rs.getString("source_schema"),
            sourceChineseName = rs.getString("source_chinese_name"),
            sourceDatasource = rs.getString("source_datasource"),
            sourceSystem = rs.getString("source_system"),
            targetTable = rs.getString("target_table"),
            targetSchema = rs.getString("target_schema"),
            targetDatasource = rs.getString("target_datasource"),
            lineageType = rs.getString("lineage_type"),
            lineageSource = rs.getString("lineage_source"),
            confidenceScore = rs.getBigDecimal("confidence_score"),
            createdAt = rs.getTimestamp("created_at")?.toLocalDateTime(),
            level = rs.getInt("level")
        )
    }

    private val columnLineageViewRowMapper = RowMapper<ColumnLineageView> { rs, _ ->
        ColumnLineageView(
            relationshipId = rs.getLong("relationship_id"),
            sourceColumn = rs.getString("source_column"),
            sourceDataType = rs.getString("source_data_type"),
            sourceTable = rs.getString("source_table"),
            sourceSchema = rs.getString("source_schema"),
            targetColumn = rs.getString("target_column"),
            targetDataType = rs.getString("target_data_type"),
            targetTable = rs.getString("target_table"),
            targetSchema = rs.getString("target_schema"),
            transformationType = rs.getString("transformation_type"),
            transformationDescription = rs.getString("transformation_description"),
            transformationExpression = rs.getString("transformation_expression"),
            confidenceScore = rs.getBigDecimal("confidence_score"),
            lineageSource = rs.getString("lineage_source"),
            createdAt = rs.getTimestamp("created_at")?.toLocalDateTime()
        )
    }

    private val systemStatisticsRowMapper = RowMapper<SystemStatistics> { rs, _ ->
        SystemStatistics(
            systemName = rs.getString("system_name"),
            systemCode = rs.getString("system_code"),
            datasourceCount = rs.getInt("datasource_count"),
            tableCount = rs.getInt("table_count"),
            columnCount = rs.getInt("column_count"),
            tableLineageCount = rs.getInt("table_lineage_count"),
            columnLineageCount = rs.getInt("column_lineage_count"),
            lastUpdateTime = rs.getTimestamp("last_update_time")?.toLocalDateTime()
        )
    }
    
    /**
     * 根据表ID查询上游血缘关系
     * 
     * @param tableId 表ID
     * @param maxLevels 最大查询层级
     * @return 表血缘视图列表
     */
    fun findUpstreamLineageByTableId(
        tableId: Long,
        maxLevels: Int = 3
    ): List<TableLineageView> {
        // 使用迭代方式查询上游血缘关系，避免使用 WITH RECURSIVE
        val result = mutableListOf<TableLineageView>()
        val processedTableIds = mutableSetOf<Long>()
        val tablesToProcess = mutableListOf<Pair<Long, Int>>() // 表ID和层级
        
        // 初始化待处理表列表
        tablesToProcess.add(Pair(tableId, 1))
        
        while (tablesToProcess.isNotEmpty() && processedTableIds.size <= 1000) { // 设置安全上限
            val (currentTableId, currentLevel) = tablesToProcess.removeAt(0)
            
            if (currentLevel > maxLevels) {
                continue
            }
            
            if (processedTableIds.contains(currentTableId)) {
                continue
            }
            
            processedTableIds.add(currentTableId)
            
            // 查询当前表的直接上游关系
            val sql = """
                SELECT 
                    lr.id as relationship_id,
                    lr.source_table_id,
                    lr.target_table_id,
                    st.table_name as source_table,
                    st.schema_name as source_schema,
                    st.chinese_name as source_chinese_name,
                    sds.datasource_name as source_datasource,
                    ssys.system_name as source_system,
                    tt.table_name as target_table,
                    tt.schema_name as target_schema,
                    tds.datasource_name as target_datasource,
                    lr.lineage_type,
                    lr.source_system as lineage_source,
                    lr.confidence_score,
                    lr.created_at,
                    ? as level
                FROM lineage_relationships lr
                JOIN lineage_tables st ON lr.source_table_id = st.id
                JOIN lineage_tables tt ON lr.target_table_id = tt.id
                JOIN lineage_datasources sds ON st.datasource_id = sds.id
                JOIN lineage_datasources tds ON tt.datasource_id = tds.id
                LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
                WHERE tt.id = ?
                  AND lr.relationship_type = 'TABLE_LEVEL'
                  AND lr.is_active = true
            """.trimIndent()
            
            val currentLevelResults = jdbcTemplate.query(
                sql,
                arrayOf(currentLevel, currentTableId),
                tableLineageViewRowMapper
            )
            
            result.addAll(currentLevelResults)
            
            // 将上游表添加到待处理列表
            currentLevelResults.forEach { lineageView ->
                tablesToProcess.add(Pair(lineageView.sourceTableId, currentLevel + 1))
            }
        }
        
        // 按层级和表名排序
        return result.sortedWith(compareBy({ it.level }, { it.sourceTable }))
    }
    
    /**
     * 根据表ID查询下游血缘关系
     * 
     * @param tableId 表ID
     * @param maxLevels 最大查询层级
     * @return 表血缘视图列表
     */
    fun findDownstreamLineageByTableId(
        tableId: Long,
        maxLevels: Int = 3
    ): List<TableLineageView> {
        // 使用迭代方式查询下游血缘关系，避免使用 WITH RECURSIVE
        val result = mutableListOf<TableLineageView>()
        val processedTableIds = mutableSetOf<Long>()
        val tablesToProcess = mutableListOf<Pair<Long, Int>>() // 表ID和层级
        
        // 初始化待处理表列表
        tablesToProcess.add(Pair(tableId, 1))
        
        while (tablesToProcess.isNotEmpty() && processedTableIds.size <= 1000) { // 设置安全上限
            val (currentTableId, currentLevel) = tablesToProcess.removeAt(0)
            
            if (currentLevel > maxLevels) {
                continue
            }
            
            if (processedTableIds.contains(currentTableId)) {
                continue
            }
            
            processedTableIds.add(currentTableId)
            
            // 查询当前表的直接下游关系
            val sql = """
                SELECT 
                    lr.id as relationship_id,
                    lr.source_table_id,
                    lr.target_table_id,
                    st.table_name as source_table,
                    st.schema_name as source_schema,
                    st.chinese_name as source_chinese_name,
                    sds.datasource_name as source_datasource,
                    ssys.system_name as source_system,
                    tt.table_name as target_table,
                    tt.schema_name as target_schema,
                    tds.datasource_name as target_datasource,
                    lr.lineage_type,
                    lr.source_system as lineage_source,
                    lr.confidence_score,
                    lr.created_at,
                    ? as level
                FROM lineage_relationships lr
                JOIN lineage_tables st ON lr.source_table_id = st.id
                JOIN lineage_tables tt ON lr.target_table_id = tt.id
                JOIN lineage_datasources sds ON st.datasource_id = sds.id
                JOIN lineage_datasources tds ON tt.datasource_id = tds.id
                LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
                WHERE st.id = ?
                  AND lr.relationship_type = 'TABLE_LEVEL'
                  AND lr.is_active = true
            """.trimIndent()
            
            val currentLevelResults = jdbcTemplate.query(
                sql,
                arrayOf(currentLevel, currentTableId),
                tableLineageViewRowMapper
            )
            
            result.addAll(currentLevelResults)
            
            // 将下游表添加到待处理列表
            currentLevelResults.forEach { lineageView ->
                tablesToProcess.add(Pair(lineageView.targetTableId, currentLevel + 1))
            }
        }
        
        // 按层级和表名排序
        return result.sortedWith(compareBy({ it.level }, { it.targetTable }))
    }
    
    /**
     * 根据表ID查询列级血缘关系
     * 
     * @param tableId 表ID
     * @return 列级血缘视图列表
     */
    fun findColumnLineageByTableId(tableId: Long): List<ColumnLineageView> {
        val sql = """
            SELECT 
                lr.id as relationship_id,
                sc.column_name as source_column,
                sc.data_type as source_data_type,
                st.table_name as source_table,
                st.schema_name as source_schema,
                tc.column_name as target_column,
                tc.data_type as target_data_type,
                tt.table_name as target_table,
                tt.schema_name as target_schema,
                lr.transformation_type,
                lr.transformation_description,
                lr.transformation_expression,
                lr.confidence_score,
                lr.source_system as lineage_source,
                lr.created_at
            FROM lineage_relationships lr
            JOIN lineage_columns sc ON lr.source_column_id = sc.id
            JOIN lineage_columns tc ON lr.target_column_id = tc.id
            JOIN lineage_tables st ON sc.table_id = st.id
            JOIN lineage_tables tt ON tc.table_id = tt.id
            WHERE lr.relationship_type = 'COLUMN_LEVEL'
              AND lr.is_active = true
              AND (st.id = ? OR tt.id = ?)
            ORDER BY st.table_name, sc.column_name, tt.table_name, tc.column_name
        """.trimIndent()

        return jdbcTemplate.query(
            sql,
            arrayOf(tableId, tableId),
            columnLineageViewRowMapper
        )
    }
    
    /**
     * 查询所有系统信息 (Query all system information with filtering)
     *
     * @param systemName 系统名称模糊搜索 (optional)
     * @param systemStatus 系统状态过滤 (optional)
     */
    fun findAllSystems(systemName: String? = null, systemStatus: String? = null): List<SystemInfo> {
        val conditions = mutableListOf<String>()
        val parameters = mutableListOf<Any>()

        // 构建WHERE条件
        if (!systemName.isNullOrBlank()) {
            conditions.add("system_name LIKE ?")
            parameters.add("%${systemName.trim()}%")
        }

        if (!systemStatus.isNullOrBlank()) {
            conditions.add("status = ?")
            parameters.add(systemStatus.trim().uppercase())
        }

        val whereClause = if (conditions.isNotEmpty()) {
            "WHERE ${conditions.joinToString(" AND ")}"
        } else {
            ""
        }

        val sql = """
            SELECT id, system_name, system_code, description, contact_person,
                   status, created_at, updated_at, cron_expression
            FROM lineage_systems
            $whereClause
            ORDER BY system_name
        """.trimIndent()

        return jdbcTemplate.query(sql, parameters.toTypedArray()) { rs, _ ->
            val cronExpressionStr = rs.getString("cron_expression")
            val nextScheduleTime = CronExpressionUtil.calculateNextScheduleTime(cronExpressionStr)

            SystemInfo(
                id = rs.getLong("id"),
                systemName = rs.getString("system_name"),
                systemCode = rs.getString("system_code"),
                description = rs.getString("description"),
                contactPerson = rs.getString("contact_person"),
                status = rs.getString("status"),
                systemStatus = rs.getString("status"), // Add systemStatus field mapped from status
                createdAt = rs.getTimestamp("created_at").toLocalDateTime(),
                updatedAt = rs.getTimestamp("updated_at").toLocalDateTime(),
                cronExpression = cronExpressionStr,
                scheduleTime = nextScheduleTime
            )
        }
    }
}

// 数据传输对象

data class TableLineageView(
    val relationshipId: Long,
    val sourceTableId: Long,
    val targetTableId: Long,
    val sourceTable: String,
    val sourceSchema: String?,
    val sourceChineseName: String?,
    val sourceDatasource: String,
    val sourceSystem: String?,
    val targetTable: String,
    val targetSchema: String?,
    val targetDatasource: String,
    val lineageType: String,
    val lineageSource: String,
    val confidenceScore: java.math.BigDecimal?,
    val createdAt: LocalDateTime?,
    val level: Int = 1
)

data class ColumnLineageView(
    val relationshipId: Long,
    val sourceColumn: String,
    val sourceDataType: String,
    val sourceTable: String,
    val sourceSchema: String?,
    val targetColumn: String,
    val targetDataType: String,
    val targetTable: String,
    val targetSchema: String?,
    val transformationType: String?,
    val transformationDescription: String?,
    val transformationExpression: String?,
    val confidenceScore: java.math.BigDecimal?,
    val lineageSource: String,
    val createdAt: LocalDateTime?
)

data class SystemStatistics(
    val systemName: String,
    val systemCode: String,
    val datasourceCount: Int,
    val tableCount: Int,
    val columnCount: Int,
    val tableLineageCount: Int,
    val columnLineageCount: Int,
    val lastUpdateTime: LocalDateTime?
)

/**
 * 系统信息数据类 (System Info Data Class)
 */
data class SystemInfo(
    val id: Long,
    val systemName: String,
    val systemCode: String,
    val description: String?,
    val contactPerson: String?,
    val status: String,
    val systemStatus: String, // Add systemStatus field for API response
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val cronExpression: String?,
    val scheduleTime: LocalDateTime?
)