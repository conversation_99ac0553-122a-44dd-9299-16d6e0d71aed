-- ====================================================================
-- 系统状态字段文档化 (System Status Field Documentation)
-- Document the existing status field usage for systemStatus API filtering
-- ====================================================================

-- 注释: 现有的 status 字段将在 API 中作为 systemStatus 返回
-- Note: The existing status field will be returned as systemStatus in the API

-- 确保状态字段有正确的索引 (已存在)
-- Ensure status field has proper index (already exists)
-- INDEX idx_system_status (status) - already exists in create_lineage_tables.sql

-- 验证状态枚举值
-- Verify status enum values
-- Current valid values: 'ACTIVE', 'INACTIVE'

-- 为了确保数据一致性，可以运行以下查询来检查现有数据
-- To ensure data consistency, run the following query to check existing data:
-- SELECT DISTINCT status FROM lineage_systems;

-- 如果需要添加新的状态值，可以使用以下语句：
-- If new status values need to be added, use the following statement:
-- ALTER TABLE lineage_systems MODIFY COLUMN status ENUM('ACTIVE', 'INACTIVE', 'MAINTENANCE', 'DEPRECATED') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态';
